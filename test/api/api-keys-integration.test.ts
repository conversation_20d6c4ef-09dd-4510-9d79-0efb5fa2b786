// Integration test for API Keys functionality
import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';

describe('API Keys Integration Tests', () => {
	let mock: MockAdapter;

	beforeEach(() => {
		mock = new MockAdapter(axios);
		// Ensure no real requests are made by default
		mock.onAny().reply(404, { error: 'Not mocked' });
	});

	afterEach(() => {
		mock.restore();
	});

	describe('API Call Test', () => {
		it('should make successful API call with base URL and authorization header', async () => {
			const baseUrl = 'https://stride.tiltedwindmillagency.com';
			const apiKey = 'm4/SY.?S=/y9SCXkg2LSRJJm2r!VwQ3KBSW9YzEVJA!E9?mh';
			// Use the unencoded URL format that actually works
			const expectedUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

			// Reset and set up specific mock for this test
			mock.reset();
			mock.onGet(expectedUrl).reply(200, {
				status: 'success',
				message: 'Template service login test passed',
				data: {
					service: 'TemplServiceLogin',
					params: [10],
					timestamp: '2024-01-15T10:30:00Z',
				},
			});

			// Make the API call
			const response = await axios.get(expectedUrl, {
				headers: {
					Authorization: apiKey,
				},
				timeout: 10000,
			});

			// Verify the response
			expect(response.status).toBe(200);
			expect(response.data).toHaveProperty('status', 'success');
			expect(response.data).toHaveProperty('message');
			expect(response.data.data).toHaveProperty('service', 'TemplServiceLogin');
			expect(response.data.data.params).toEqual([10]);

			// Verify the request was made with correct headers
			expect(mock.history.get).toHaveLength(1);
			expect(mock.history.get[0].headers).toHaveProperty('Authorization', apiKey);
		});

		it('should handle API errors gracefully', async () => {
			const baseUrl = 'https://stride.tiltedwindmillagency.com';
			const apiKey = 'invalid-key';
			// Use the unencoded URL format that actually works
			const expectedUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

			// Reset and set up specific mock for this test
			mock.reset();
			mock.onGet(expectedUrl).reply(401, {
				error: 'Unauthorized',
				message: 'Invalid API key',
			});

			// Make the API call and expect it to throw
			await expect(
				axios.get(expectedUrl, {
					headers: {
						Authorization: apiKey,
					},
					timeout: 10000,
				}),
			).rejects.toThrow();

			// Verify the request was made
			expect(mock.history.get).toHaveLength(1);
		});

		it('should handle network errors', async () => {
			const baseUrl = 'https://invalid-domain.com';
			const apiKey = 'm4/SY.?S=/y9SCXkg2LSRJJm2r!VwQ3KBSW9YzEVJA!E9?mh';
			const expectedUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

			// Reset and set up specific mock for this test
			mock.reset();
			mock.onGet(expectedUrl).networkError();

			// Make the API call and expect it to throw
			await expect(
				axios.get(expectedUrl, {
					headers: {
						Authorization: apiKey,
						'Content-Type': 'application/json',
					},
					timeout: 10000,
				}),
			).rejects.toThrow();
		});

		it('should handle timeout errors', async () => {
			const baseUrl = 'https://stride.tiltedwindmillagency.com';
			const apiKey = 'm4/SY.?S=/y9SCXkg2LSRJJm2r!VwQ3KBSW9YzEVJA!E9?mh';
			// Use the unencoded URL format that actually works
			const expectedUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

			// Reset and set up specific mock for this test
			mock.reset();
			mock.onGet(expectedUrl).timeout();

			// Make the API call and expect it to throw
			await expect(
				axios.get(expectedUrl, {
					headers: {
						Authorization: apiKey,
					},
					timeout: 10000,
				}),
			).rejects.toThrow();
		});
	});

	describe('URL Construction', () => {
		it('should construct correct URL with unencoded parameters', () => {
			const baseUrl = 'https://stride.tiltedwindmillagency.com';
			const endpoint = '/api.php';
			const route = 'system/test/TemplServiceLogin';
			const params = '[10]';

			// Test the URL format that actually works (unencoded)
			const expectedUrl = `${baseUrl}${endpoint}?r=${route}&params=${params}`;
			const actualUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

			expect(actualUrl).toBe(expectedUrl);
		});
	});
});
