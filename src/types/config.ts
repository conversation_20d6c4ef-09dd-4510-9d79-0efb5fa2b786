export type Environment = 'development' | 'production' | 'staging';

export type ConfigValidationError = {
  field: string;
  message: string;
  required: boolean;
};

export type AppConfig = {
  environment: Environment;
  version: string;
  buildNumber: string;
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  auth: {
    tokenStorageKey: string;
    refreshTokenStorageKey: string;
    sessionTimeout: number; // minutes
  };
  analytics: {
    enabled: boolean;
    trackingId?: string;
    debugMode: boolean;
  };
  features: {
    enablePushNotifications: boolean;
    enableAnalytics: boolean;
    enableOfflineMode: boolean;
    enableBetaFeatures: boolean;
    enableDebugMenu: boolean;
  };
  services: {
    firebase?: {
      apiKey: string;
      authDomain?: string;
      projectId?: string;
    };
    stripe?: {
      publishableKey: string;
    };
    sentry?: {
      dsn: string;
    };
  };
};
