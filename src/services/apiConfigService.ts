/**
 * API Configuration Service
 * 
 * Handles all business logic for API key and URL management,
 * including secure storage, validation, and testing.
 */

import { secureStorageRepository } from '../repositories/secureStorageRepository';
import axios from 'axios';

// __DEV__ is a global variable in React Native
declare const __DEV__: boolean;

// Storage keys for API configuration
export const API_CONFIG_KEYS = {
  API_KEY: 'test_api_key',
  API_BASE_URL: 'api_base_url',
} as const;

// Types
export interface ApiConfig {
  apiKey: string | null;
  apiBaseUrl: string | null;
}

export interface TestResults {
  save: boolean | null;
  retrieve: boolean | null;
  delete: boolean | null;
  exists: boolean | null;
  saveUrl: boolean | null;
  retrieveUrl: boolean | null;
  apiTest: boolean | null;
}

export interface ApiTestResponse {
  success: boolean;
  status?: number;
  data?: any;
  error?: string;
}

export interface DevConfig {
  DEV_UNA_API_KEY?: string | null;
  DEV_UNA_API_BASE_URL?: string | null;
}

/**
 * API Configuration Service
 */
class ApiConfigService {
  /**
   * Load stored API configuration
   */
  async loadApiConfig(): Promise<ApiConfig> {
    try {
      const [apiKey, apiBaseUrl] = await Promise.all([
        secureStorageRepository.getItem(API_CONFIG_KEYS.API_KEY),
        secureStorageRepository.getItem(API_CONFIG_KEYS.API_BASE_URL),
      ]);

      return {
        apiKey,
        apiBaseUrl,
      };
    } catch (error) {
      console.error('Failed to load API configuration:', error);
      return {
        apiKey: null,
        apiBaseUrl: null,
      };
    }
  }

  /**
   * Save API key securely
   */
  async saveApiKey(apiKey: string): Promise<void> {
    if (!apiKey.trim()) {
      throw new Error('API key cannot be empty');
    }

    try {
      await secureStorageRepository.setItem(API_CONFIG_KEYS.API_KEY, apiKey.trim());
    } catch (error) {
      console.error('Failed to save API key:', error);
      throw new Error(`Failed to save API key: ${error}`);
    }
  }

  /**
   * Save API base URL securely
   */
  async saveApiBaseUrl(apiBaseUrl: string): Promise<void> {
    if (!apiBaseUrl.trim()) {
      throw new Error('API base URL cannot be empty');
    }

    // Validate URL format
    try {
      new URL(apiBaseUrl.trim());
    } catch {
      throw new Error('Please enter a valid URL (e.g., https://api.example.com)');
    }

    try {
      await secureStorageRepository.setItem(API_CONFIG_KEYS.API_BASE_URL, apiBaseUrl.trim());
    } catch (error) {
      console.error('Failed to save API base URL:', error);
      throw new Error(`Failed to save API base URL: ${error}`);
    }
  }

  /**
   * Test API key retrieval
   */
  async testRetrieveApiKey(): Promise<{ success: boolean; retrievedKey?: string; error?: string }> {
    try {
      const retrieved = await secureStorageRepository.getItem(API_CONFIG_KEYS.API_KEY);
      return {
        success: true,
        retrievedKey: retrieved || undefined,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to retrieve API key: ${error}`,
      };
    }
  }

  /**
   * Test API base URL retrieval
   */
  async testRetrieveApiBaseUrl(): Promise<{ success: boolean; retrievedUrl?: string; error?: string }> {
    try {
      const retrieved = await secureStorageRepository.getItem(API_CONFIG_KEYS.API_BASE_URL);
      return {
        success: true,
        retrievedUrl: retrieved || undefined,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to retrieve API base URL: ${error}`,
      };
    }
  }

  /**
   * Test if API key exists
   */
  async testApiKeyExists(): Promise<{ success: boolean; exists: boolean; error?: string }> {
    try {
      const exists = await secureStorageRepository.hasItem(API_CONFIG_KEYS.API_KEY);
      return {
        success: true,
        exists,
      };
    } catch (error) {
      return {
        success: false,
        exists: false,
        error: `Failed to check key existence: ${error}`,
      };
    }
  }

  /**
   * Delete all API configuration
   */
  async deleteApiConfig(): Promise<void> {
    try {
      await Promise.all([
        secureStorageRepository.removeItem(API_CONFIG_KEYS.API_KEY),
        secureStorageRepository.removeItem(API_CONFIG_KEYS.API_BASE_URL),
      ]);
    } catch (error) {
      console.error('Failed to delete API configuration:', error);
      throw new Error(`Failed to delete API configuration: ${error}`);
    }
  }

  /**
   * Test API call
   */
  async testApiCall(apiKey: string, apiBaseUrl: string): Promise<ApiTestResponse> {
    if (!apiKey || !apiBaseUrl) {
      return {
        success: false,
        error: 'Both API key and base URL are required',
      };
    }

    try {
      // Construct the test URL
      const baseUrl = apiBaseUrl.replace(/\/$/, '');
      const testUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

      console.log('🚀 Testing API call to:', testUrl);

      const response = await axios.get(testUrl, {
        headers: {
          Authorization: apiKey,
        },
        timeout: 10000,
      });

      console.log('✅ API test successful:', response.status);

      return {
        success: true,
        status: response.status,
        data: response.data,
      };
    } catch (error: any) {
      console.log('❌ API test failed:', error);

      let errorMessage = 'Unknown error occurred';
      let responseData = null;
      let status = undefined;

      if (error.response) {
        // Server responded with error status
        status = error.response.status;
        responseData = error.response.data;
        errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`;
      } else if (error.request) {
        // Request was made but no response received
        errorMessage = 'No response received from server';
      } else {
        // Something else happened
        errorMessage = error.message;
      }

      return {
        success: false,
        status,
        data: responseData,
        error: errorMessage,
      };
    }
  }

  /**
   * Load development configuration (only in development mode)
   */
  async loadDevConfig(forceReload = false): Promise<{ success: boolean; message: string; config?: DevConfig }> {
    if (!__DEV__) {
      return {
        success: false,
        message: 'Development configuration is only available in development mode',
      };
    }

    try {
      console.log('🔧 Loading dev.config.json...');

      // Since we can't reliably load dev.config.json without Metro bundler issues,
      // we'll just return a helpful message and let users manually configure
      console.log('ℹ️ Dev config auto-loading disabled to prevent Metro bundler errors');
      return {
        success: false,
        message: 'Development config auto-loading is disabled. Please manually enter your API key and base URL, or restart the development server if you have a dev.config.json file.',
        config: {
          DEV_UNA_API_KEY: null,
          DEV_UNA_API_BASE_URL: null,
        } as any,
      };

      // Check if the loaded config has the required fields
      if (!devConfig || !devConfig.DEV_UNA_API_KEY || !devConfig.DEV_UNA_API_BASE_URL) {
        return {
          success: false,
          message: 'dev.config.json found but missing required fields (DEV_UNA_API_KEY, DEV_UNA_API_BASE_URL)',
          config: {
            DEV_UNA_API_KEY: devConfig?.DEV_UNA_API_KEY || null,
            DEV_UNA_API_BASE_URL: devConfig?.DEV_UNA_API_BASE_URL || null,
          } as any,
        };
      }

      // Check if we already have stored values
      const existingConfig = await this.loadApiConfig();
      const hasExistingConfig = existingConfig.apiKey && existingConfig.apiBaseUrl;

      // Auto-load if we don't have existing values, or if force reload is requested
      if (!hasExistingConfig || forceReload) {
        await Promise.all([
          this.saveApiKey(devConfig.DEV_UNA_API_KEY),
          this.saveApiBaseUrl(devConfig.DEV_UNA_API_BASE_URL),
        ]);

        const action = forceReload ? 'reloaded' : 'loaded';
        console.log('✅ Dev configuration loaded successfully');

        return {
          success: true,
          message: `API key and base URL ${action} from dev.config.json`,
          config: devConfig,
        };
      } else {
        return {
          success: true,
          message: 'Existing configuration found, skipping dev config auto-load',
          config: devConfig,
        };
      }
    } catch (error: any) {
      console.log('ℹ️ Error processing dev config:', error.message);
      return {
        success: false,
        message: 'Error processing development configuration. Check console for details.',
        config: {
          DEV_UNA_API_KEY: null,
          DEV_UNA_API_BASE_URL: null,
        } as any,
      };
    }
  }

  /**
   * Run comprehensive test suite
   */
  async runAllTests(): Promise<{ results: TestResults; apiResponse?: string; allPassed: boolean }> {
    const config = await this.loadApiConfig();
    
    if (!config.apiKey) {
      throw new Error('Please save an API key first');
    }

    const results: TestResults = {
      save: null,
      retrieve: null,
      delete: null,
      exists: null,
      saveUrl: null,
      retrieveUrl: null,
      apiTest: null,
    };

    let apiResponse = '';

    try {
      // Test 1: Save operation (already done)
      results.save = true;

      // Test 2: Retrieve API key
      const retrieveKeyResult = await this.testRetrieveApiKey();
      results.retrieve = retrieveKeyResult.success && retrieveKeyResult.retrievedKey === config.apiKey;

      // Test 3: Retrieve API base URL
      const retrieveUrlResult = await this.testRetrieveApiBaseUrl();
      results.retrieveUrl = retrieveUrlResult.success && retrieveUrlResult.retrievedUrl === config.apiBaseUrl;

      // Test 4: Exists check
      const existsResult = await this.testApiKeyExists();
      results.exists = existsResult.success && existsResult.exists;

      // Test 5: API call test (if both key and URL are available)
      if (config.apiBaseUrl) {
        const apiTestResult = await this.testApiCall(config.apiKey, config.apiBaseUrl);
        results.apiTest = apiTestResult.success;
        
        if (apiTestResult.success) {
          apiResponse = `Status: ${apiTestResult.status}\n\nResponse:\n${JSON.stringify(apiTestResult.data, null, 2)}`;
        } else {
          apiResponse = apiTestResult.status 
            ? `Status: ${apiTestResult.status}\n\nResponse:\n${JSON.stringify(apiTestResult.data, null, 2)}`
            : `Error: ${apiTestResult.error}`;
        }
      }

      const allPassed = Boolean(results.retrieve && results.exists &&
        (config.apiBaseUrl ? results.retrieveUrl && results.apiTest : true));

      return {
        results,
        apiResponse,
        allPassed,
      };
    } catch (error) {
      console.error('Test suite failed:', error);
      throw new Error(`Test suite failed: ${error}`);
    }
  }
}

// Export singleton instance
export const apiConfigService = new ApiConfigService();
