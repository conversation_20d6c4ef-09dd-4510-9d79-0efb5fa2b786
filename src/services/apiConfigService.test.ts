/**
 * Tests for ApiConfigService
 */

import { apiConfigService } from './apiConfigService';
import { secureStorageRepository } from '../repositories/secureStorageRepository';
import axios from 'axios';

// Mock dependencies
jest.mock('../repositories/secureStorageRepository');
jest.mock('axios');

const mockSecureStorageRepository = secureStorageRepository as jest.Mocked<typeof secureStorageRepository>;
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('ApiConfigService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('loadApiConfig', () => {
    it('should load API configuration from secure storage', async () => {
      mockSecureStorageRepository.getItem
        .mockResolvedValueOnce('test-api-key')
        .mockResolvedValueOnce('https://api.example.com');

      const result = await apiConfigService.loadApiConfig();

      expect(result).toEqual({
        apiKey: 'test-api-key',
        apiBaseUrl: 'https://api.example.com',
      });
    });

    it('should handle missing configuration gracefully', async () => {
      mockSecureStorageRepository.getItem
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(null);

      const result = await apiConfigService.loadApiConfig();

      expect(result).toEqual({
        apiKey: null,
        apiBaseUrl: null,
      });
    });
  });

  describe('saveApiKey', () => {
    it('should save API key to secure storage', async () => {
      mockSecureStorageRepository.setItem.mockResolvedValue();

      await apiConfigService.saveApiKey('test-key');

      expect(mockSecureStorageRepository.setItem).toHaveBeenCalledWith('test_api_key', 'test-key');
    });

    it('should throw error for empty API key', async () => {
      await expect(apiConfigService.saveApiKey('')).rejects.toThrow('API key cannot be empty');
    });

    it('should trim whitespace from API key', async () => {
      mockSecureStorageRepository.setItem.mockResolvedValue();

      await apiConfigService.saveApiKey('  test-key  ');

      expect(mockSecureStorageRepository.setItem).toHaveBeenCalledWith('test_api_key', 'test-key');
    });
  });

  describe('saveApiBaseUrl', () => {
    it('should save valid API base URL to secure storage', async () => {
      mockSecureStorageRepository.setItem.mockResolvedValue();

      await apiConfigService.saveApiBaseUrl('https://api.example.com');

      expect(mockSecureStorageRepository.setItem).toHaveBeenCalledWith('api_base_url', 'https://api.example.com');
    });

    it('should throw error for empty URL', async () => {
      await expect(apiConfigService.saveApiBaseUrl('')).rejects.toThrow('API base URL cannot be empty');
    });

    it('should throw error for invalid URL', async () => {
      await expect(apiConfigService.saveApiBaseUrl('not-a-url')).rejects.toThrow('Please enter a valid URL');
    });
  });

  describe('testRetrieveApiKey', () => {
    it('should successfully retrieve API key', async () => {
      mockSecureStorageRepository.getItem.mockResolvedValue('test-key');

      const result = await apiConfigService.testRetrieveApiKey();

      expect(result).toEqual({
        success: true,
        retrievedKey: 'test-key',
      });
    });

    it('should handle retrieval errors', async () => {
      mockSecureStorageRepository.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await apiConfigService.testRetrieveApiKey();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to retrieve API key');
    });
  });

  describe('testApiCall', () => {
    it('should make successful API call', async () => {
      const mockResponse = {
        status: 200,
        data: { success: true },
      };
      mockAxios.get.mockResolvedValue(mockResponse);

      const result = await apiConfigService.testApiCall('test-key', 'https://api.example.com');

      expect(result).toEqual({
        success: true,
        status: 200,
        data: { success: true },
      });
    });

    it('should handle API call errors', async () => {
      const mockError = {
        response: {
          status: 401,
          statusText: 'Unauthorized',
          data: { error: 'Invalid API key' },
        },
      };
      mockAxios.get.mockRejectedValue(mockError);

      const result = await apiConfigService.testApiCall('invalid-key', 'https://api.example.com');

      expect(result).toEqual({
        success: false,
        status: 401,
        data: { error: 'Invalid API key' },
        error: 'HTTP 401: Unauthorized',
      });
    });

    it('should handle network errors', async () => {
      const mockError = {
        request: {},
        message: 'Network Error',
      };
      mockAxios.get.mockRejectedValue(mockError);

      const result = await apiConfigService.testApiCall('test-key', 'https://api.example.com');

      expect(result).toEqual({
        success: false,
        status: undefined,
        data: null,
        error: 'No response received from server',
      });
    });
  });

  describe('deleteApiConfig', () => {
    it('should delete both API key and base URL', async () => {
      mockSecureStorageRepository.removeItem.mockResolvedValue();

      await apiConfigService.deleteApiConfig();

      expect(mockSecureStorageRepository.removeItem).toHaveBeenCalledWith('test_api_key');
      expect(mockSecureStorageRepository.removeItem).toHaveBeenCalledWith('api_base_url');
    });
  });

  describe('loadDevConfig', () => {
    it('should handle dev config loading gracefully', async () => {
      // Mock __DEV__ to be true
      (global as any).__DEV__ = true;

      const result = await apiConfigService.loadDevConfig();

      expect(result.success).toBe(false);
      expect(result.message).toContain('Development config auto-loading is disabled');
      expect(result.config).toEqual({
        DEV_UNA_API_KEY: null,
        DEV_UNA_API_BASE_URL: null,
      });
    });

    it('should return error in production mode', async () => {
      // Mock __DEV__ to be false
      (global as any).__DEV__ = false;

      const result = await apiConfigService.loadDevConfig();

      expect(result.success).toBe(false);
      expect(result.message).toContain('only available in development mode');
    });
  });
});
