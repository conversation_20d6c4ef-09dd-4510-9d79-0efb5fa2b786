import { Image } from 'expo-image';
import { useState } from 'react';
import { Platform, StyleSheet } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import CustomTextField from '@/components/ui/CustomTextField';
import { useRouter } from 'expo-router';
import Config from '@/config';

export default function HomeScreen() {
	const [waveTrigger, setWaveTrigger] = useState(0);
	const router = useRouter();

	return (
		<ParallaxScrollView
			headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
			headerImage={
				<Image source={require('@assets/images/logo.svg')} style={styles.reactLogo} />
			}
		>
			<ThemedView style={styles.titleContainer}>
				<CustomTextField variant='primary'>Welcome</CustomTextField>
				<HelloWave waveTrigger={waveTrigger} />
				<CustomButton variant='primary' onPress={() => setWaveTrigger((prev) => prev + 1)}>
					Wave Again
				</CustomButton>
			</ThemedView>
			<ThemedView style={styles.stepContainer}>
				<ThemedText type='title'>Config</ThemedText>
				<ThemedText>{Config.ENV_NAME}</ThemedText>
				<ThemedText>{Config.API_URL}</ThemedText>
				<ThemedText>{Config.CONFIG_VERSION}</ThemedText>
			</ThemedView>
			<ThemedView style={styles.stepContainer}>
				<ThemedText type='title'>Navigation</ThemedText>
				<CustomButton
					variant='primary'
					onPress={() => {
						router.push('hello');
					}}
				>
					Click to navigate
				</CustomButton>
			</ThemedView>
		</ParallaxScrollView>
	);
}

const styles = StyleSheet.create({
	titleContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: 8,
	},
	stepContainer: {
		gap: 8,
		marginBottom: 8,
	},
	reactLogo: {
		height: 48,
		width: 96,
		top: 30,
		left: 20,
		position: 'absolute',
	},
});
